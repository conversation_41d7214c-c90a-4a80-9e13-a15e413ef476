#!/usr/bin/env python3
"""
快速测试本地模型
"""

import os
import torch

def quick_test():
    print("🚀 快速测试本地模型")
    print("=" * 40)
    
    # 检查CUDA
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 检查本地模型
    model_path = "./Qwen-Image-DF11"
    if os.path.exists(model_path):
        print(f"✅ 本地模型存在: {model_path}")
        
        # 检查文件大小
        safetensors_path = os.path.join(model_path, "diffusion_pytorch_model.safetensors")
        if os.path.exists(safetensors_path):
            size = os.path.getsize(safetensors_path) / (1024**3)
            print(f"✅ 模型文件: {size:.1f} GB")
        else:
            print("❌ 模型文件不存在")
            return False
    else:
        print("❌ 本地模型不存在")
        return False
    
    # 测试基本导入
    try:
        print("📦 测试导入...")
        from diffusers import QwenImageTransformer2DModel
        print("✅ diffusers导入成功")
        
        from dfloat11 import DFloat11Model
        print("✅ dfloat11导入成功")
        
        print("✅ 所有导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

if __name__ == "__main__":
    if quick_test():
        print("\n🎉 快速测试通过！")
        print("💡 可以尝试运行图像生成")
    else:
        print("\n❌ 快速测试失败")
