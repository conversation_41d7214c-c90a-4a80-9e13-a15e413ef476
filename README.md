# Qwen-Image DFloat11 部署指南

这是一个基于DFloat11压缩技术的Qwen-Image模型部署项目。DFloat11压缩可以将模型大小减少32%，同时保持100%的输出精度，并支持在16GB GPU上运行。

## 🚀 特性

- **内存高效**: 通过DFloat11压缩，模型大小从41GB减少到28.42GB
- **GPU友好**: 支持在16GB GPU上运行（使用CPU offloading）
- **无损压缩**: 保持与原始BFloat16模型完全相同的输出
- **灵活配置**: 支持多种宽高比和生成参数

## 📋 系统要求

- **GPU**: NVIDIA GPU with CUDA 12.8 support (推荐16GB+ VRAM)
- **内存**: 至少32GB RAM (使用CPU offloading时)
- **存储**: 至少50GB可用空间
- **操作系统**: Windows/Linux/macOS

## 🛠️ 安装步骤

### 1. 创建Conda环境

```bash
conda create -n qw python=3.10 -y
conda activate qw
```

### 2. 安装PyTorch (CUDA 12.8)

```bash
pip install torch==2.8.0 torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128
```

### 3. 安装依赖包

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install -U dfloat11[cuda12]
pip install git+https://github.com/huggingface/diffusers
```

## 🎯 使用方法

### 基本用法

```bash
# 在32GB GPU上运行（无CPU offloading）
python qwen_image.py

# 在16GB GPU上运行（启用CPU offloading）
python qwen_image.py --cpu_offload
```

### 高级参数

```bash
python qwen_image.py \
    --prompt "一只可爱的熊猫在竹林中玩耍" \
    --aspect_ratio "16:9" \
    --num_inference_steps 50 \
    --true_cfg_scale 4.0 \
    --seed 42 \
    --output "panda.png" \
    --language "zh" \
    --cpu_offload
```

### 参数说明

- `--prompt`: 图像生成提示词
- `--negative_prompt`: 负面提示词
- `--aspect_ratio`: 宽高比 (1:1, 16:9, 9:16, 4:3, 3:4)
- `--num_inference_steps`: 推理步数 (默认50)
- `--true_cfg_scale`: CFG引导强度 (默认4.0)
- `--seed`: 随机种子 (默认42)
- `--output`: 输出文件路径 (默认example.png)
- `--language`: 提示词语言 (en/zh)
- `--cpu_offload`: 启用CPU offloading (16GB GPU必需)
- `--cpu_offload_blocks`: 指定offload的transformer块数量
- `--no_pin_memory`: 禁用内存固定 (最节省内存但可能较慢)

## 💡 内存优化建议

### 32GB GPU
```bash
python qwen_image.py
```
- 峰值GPU内存: ~29.74GB
- 生成时间: ~100秒 (A100)

### 16GB GPU
```bash
python qwen_image.py --cpu_offload
```
- 峰值GPU内存: ~16.68GB
- 生成时间: ~260秒 (A100)

### 内存不足时
```bash
# 限制offload块数量
python qwen_image.py --cpu_offload --cpu_offload_blocks 16

# 禁用内存固定（最节省内存）
python qwen_image.py --cpu_offload --no_pin_memory
```

## 📊 性能对比

| 配置 | 模型大小 | 峰值GPU内存 | 生成时间 |
|------|----------|-------------|----------|
| 原始BFloat16 | ~41GB | OOM | - |
| DFloat11 | 28.42GB | 29.74GB | 100秒 |
| DFloat11 + CPU Offload | 28.42GB | 16.68GB | 260秒 |

## 🔧 故障排除

### CUDA内存不足
- 使用 `--cpu_offload` 参数
- 减少 `--cpu_offload_blocks` 数量
- 使用 `--no_pin_memory` 参数

### CPU内存不足
- 增加系统RAM
- 使用 `--no_pin_memory` 参数
- 减少 `--cpu_offload_blocks` 数量

### 生成速度慢
- 确保GPU驱动和CUDA版本正确
- 检查GPU利用率
- 考虑减少推理步数

## 📝 示例提示词

### 中文提示词
```
一只可爱的熊猫在竹林中玩耍，阳光透过竹叶洒下斑驳的光影
古代中国山水画风格的风景，有山峰、云雾和古松
现代都市夜景，霓虹灯闪烁，车流如织
```

### 英文提示词
```
A cute panda playing in a bamboo forest with sunlight filtering through leaves
Ancient Chinese landscape painting style with mountains, clouds and pine trees
Modern city night scene with neon lights and busy traffic
```

## 📄 许可证

本项目基于原始Qwen-Image模型的许可证。请参考相关模型的使用条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 支持

如果遇到问题，请：
1. 检查GPU和CUDA版本兼容性
2. 确认所有依赖包正确安装
3. 查看错误日志获取详细信息
