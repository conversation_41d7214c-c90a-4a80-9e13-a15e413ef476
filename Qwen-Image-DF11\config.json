{"dfloat11_config": {"bytes_per_thread": 8, "pattern_dict": {"transformer_blocks\\.\\d+": ["img_mod.1", "attn.to_q", "attn.to_k", "attn.to_v", "attn.add_k_proj", "attn.add_v_proj", "attn.add_q_proj", "attn.to_out.0", "attn.to_add_out", "img_mlp.net.0.proj", "img_mlp.net.2", "txt_mod.1", "txt_mlp.net.0.proj", "txt_mlp.net.2"]}, "threads_per_block": [512], "version": "0.3.1"}, "model_type": "qwen2_5_vl"}