#!/usr/bin/env python3
"""
优化的图像生成脚本 - 解决内存问题
"""

import torch
import gc
import os
from diffusers import DiffusionPipeline, QwenImageTransformer2DModel
from transformers.modeling_utils import no_init_weights
from dfloat11 import DFloat11Model
import time

def optimize_memory():
    """优化内存设置"""
    print("🔧 优化内存设置...")
    
    # 清理Python垃圾回收
    gc.collect()
    
    # 清理GPU内存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
    
    # 设置环境变量以优化内存使用
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'
    
    print("✅ 内存优化完成")

def generate_with_memory_optimization():
    print("🎨 开始内存优化的图像生成")
    print("=" * 60)
    
    # 优化内存
    optimize_memory()
    
    # 设置参数
    prompt = "A cute panda playing in bamboo forest"
    output_path = "optimized_output.png"
    local_model_path = "./Qwen-Image-DF11"
    
    print(f"📝 提示词: {prompt}")
    print(f"💾 输出路径: {output_path}")
    print(f"📁 本地模型: {local_model_path}")
    
    try:
        print("\n📥 步骤1: 加载基础模型配置...")
        model_name = "Qwen/Qwen-Image"
        
        with no_init_weights():
            transformer = QwenImageTransformer2DModel.from_config(
                QwenImageTransformer2DModel.load_config(
                    model_name, subfolder="transformer",
                ),
            ).to(torch.bfloat16)
        
        print("✅ 基础模型配置加载完成")
        
        print("\n📥 步骤2: 尝试加载DFloat11权重（最大CPU offloading）...")
        
        # 使用最激进的CPU offloading设置
        try:
            DFloat11Model.from_pretrained(
                local_model_path,
                device="cpu",
                cpu_offload=True,
                cpu_offload_blocks=60,  # 所有60个transformer块都offload到CPU
                pin_memory=False,  # 禁用内存固定
                bfloat16_model=transformer,
            )
            print("✅ DFloat11权重加载完成（全CPU offload）")
            
        except Exception as e:
            print(f"⚠️  全CPU offload失败: {e}")
            print("🔄 尝试减少offload块数...")
            
            # 尝试减少offload块数
            DFloat11Model.from_pretrained(
                local_model_path,
                device="cpu",
                cpu_offload=True,
                cpu_offload_blocks=30,  # 只offload一半的块
                pin_memory=False,
                bfloat16_model=transformer,
            )
            print("✅ DFloat11权重加载完成（部分CPU offload）")
        
        # 再次清理内存
        optimize_memory()
        
        print("\n📥 步骤3: 创建Pipeline...")
        pipe = DiffusionPipeline.from_pretrained(
            model_name,
            transformer=transformer,
            torch_dtype=torch.bfloat16,
        )
        pipe.enable_model_cpu_offload()
        
        print("✅ Pipeline创建完成")
        
        print("\n🎨 步骤4: 生成图像（小尺寸测试）...")
        start_time = time.time()
        
        # 使用较小的尺寸进行测试
        width, height = 832, 464  # 16:9 但较小尺寸
        num_steps = 15  # 更少的步数
        
        print(f"📐 图像尺寸: {width}x{height} (小尺寸测试)")
        print(f"🔢 推理步数: {num_steps}")
        
        image = pipe(
            prompt=prompt + " Ultra HD, 4K, cinematic composition.",
            negative_prompt=" ",
            width=width,
            height=height,
            num_inference_steps=num_steps,
            true_cfg_scale=3.5,  # 稍微降低CFG scale
            generator=torch.Generator(device="cuda").manual_seed(42)
        ).images[0]
        
        end_time = time.time()
        generation_time = end_time - start_time
        
        # 保存图像
        image.save(output_path)
        
        print(f"\n🎉 图像生成成功！")
        print(f"💾 保存到: {output_path}")
        print(f"⏱️  生成时间: {generation_time:.1f} 秒")
        
        if torch.cuda.is_available():
            max_memory = torch.cuda.max_memory_allocated() / 1024**3
            print(f"📊 最大GPU内存使用: {max_memory:.2f} GB")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        print("\n💡 建议解决方案:")
        print("1. 增加Windows虚拟内存（页面文件）到至少64GB")
        print("2. 关闭其他占用内存的程序")
        print("3. 重启计算机释放内存")
        import traceback
        traceback.print_exc()
        return False

def check_system_memory():
    """检查系统内存"""
    print("🔍 检查系统内存状态")
    print("=" * 40)
    
    try:
        import psutil
        
        # 检查物理内存
        memory = psutil.virtual_memory()
        print(f"📊 物理内存:")
        print(f"   总计: {memory.total / 1024**3:.1f} GB")
        print(f"   可用: {memory.available / 1024**3:.1f} GB")
        print(f"   使用率: {memory.percent:.1f}%")
        
        # 检查虚拟内存
        swap = psutil.swap_memory()
        print(f"📊 虚拟内存:")
        print(f"   总计: {swap.total / 1024**3:.1f} GB")
        print(f"   可用: {swap.free / 1024**3:.1f} GB")
        print(f"   使用率: {swap.percent:.1f}%")
        
        # 建议
        if swap.total < 50 * 1024**3:  # 小于50GB
            print("⚠️  建议增加虚拟内存到至少64GB")
        
        if memory.available < 8 * 1024**3:  # 小于8GB
            print("⚠️  可用物理内存不足，建议关闭其他程序")
            
    except ImportError:
        print("⚠️  无法检查内存状态（需要psutil库）")

if __name__ == "__main__":
    check_system_memory()
    print()
    
    if generate_with_memory_optimization():
        print("\n✅ 优化生成测试成功！")
    else:
        print("\n❌ 优化生成测试失败")
        print("\n📋 故障排除步骤:")
        print("1. 右键'此电脑' -> 属性 -> 高级系统设置")
        print("2. 性能 -> 设置 -> 高级 -> 虚拟内存 -> 更改")
        print("3. 取消'自动管理'，选择'自定义大小'")
        print("4. 设置初始大小和最大大小都为65536MB (64GB)")
        print("5. 重启计算机")
