#!/usr/bin/env python3
"""
简化版模型下载脚本
"""

import os
from huggingface_hub import snapshot_download

def get_cache_dir():
    """获取HuggingFace缓存目录"""
    cache_dir = os.path.expanduser("~/.cache/huggingface/hub")
    print(f"HuggingFace缓存目录: {cache_dir}")
    return cache_dir

def download_models():
    """下载模型"""
    print("🚀 开始下载Qwen-Image DFloat11模型")
    print("=" * 50)
    
    models = [
        ("Qwen/Qwen-Image", "基础模型"),
        ("DFloat11/Qwen-Image-DF11", "DFloat11压缩模型")
    ]
    
    for model_id, description in models:
        print(f"\n📥 下载 {description}: {model_id}")
        try:
            local_dir = snapshot_download(
                repo_id=model_id,
                cache_dir=get_cache_dir(),
                resume_download=True
            )
            print(f"✅ {description}下载完成")
            print(f"📁 本地路径: {local_dir}")
        except Exception as e:
            print(f"❌ {description}下载失败: {e}")
            return False
    
    print("\n🎉 所有模型下载完成！")
    return True

def check_downloaded_models():
    """检查已下载的模型"""
    cache_dir = get_cache_dir()
    print(f"\n📊 检查已下载的模型文件:")
    
    models = [
        ("models--Qwen--Qwen-Image", "Qwen/Qwen-Image"),
        ("models--DFloat11--Qwen-Image-DF11", "DFloat11/Qwen-Image-DF11")
    ]
    
    total_size = 0
    for dir_name, model_name in models:
        model_path = os.path.join(cache_dir, dir_name)
        if os.path.exists(model_path):
            size = get_dir_size(model_path)
            total_size += size
            print(f"✅ {model_name}: {size/1024**3:.1f} GB")
        else:
            print(f"❌ {model_name}: 未下载")
    
    print(f"📊 总计: {total_size/1024**3:.1f} GB")
    return total_size > 0

def get_dir_size(path):
    """获取目录大小"""
    total = 0
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total += os.path.getsize(filepath)
    except:
        pass
    return total

def main():
    """主函数"""
    print("🚀 Qwen-Image DFloat11 模型下载器")
    print("=" * 50)
    
    # 检查已下载的模型
    if check_downloaded_models():
        print("\n💡 发现已下载的模型文件")
        response = input("是否重新下载？(y/n): ").lower().strip()
        if response != 'y':
            print("跳过下载")
            return
    
    # 下载模型
    if download_models():
        print("\n✅ 模型下载完成！现在可以运行:")
        print("python qwen_image.py --cpu_offload --prompt '一只可爱的熊猫' --output test.png")
    else:
        print("\n❌ 模型下载失败")

if __name__ == "__main__":
    main()
