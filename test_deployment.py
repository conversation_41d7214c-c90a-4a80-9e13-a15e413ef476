#!/usr/bin/env python3
"""
测试Qwen-Image DFloat11模型部署
"""

import sys
import torch
import subprocess
import importlib.util

def check_cuda():
    """检查CUDA可用性"""
    print("🔍 检查CUDA环境...")
    if torch.cuda.is_available():
        print(f"✅ CUDA可用: {torch.version.cuda}")
        print(f"✅ GPU设备: {torch.cuda.get_device_name(0)}")
        print(f"✅ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        return True
    else:
        print("❌ CUDA不可用")
        return False

def check_packages():
    """检查必要的包是否安装"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        'torch',
        'torchvision', 
        'torchaudio',
        'diffusers',
        'transformers',
        'dfloat11',
        'accelerate',
        'safetensors',
        'huggingface_hub',
        'cupy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cupy':
                # 检查cupy-cuda12x
                import cupy
                print(f"✅ {package}: {cupy.__version__}")
            else:
                module = importlib.import_module(package)
                version = getattr(module, '__version__', 'unknown')
                print(f"✅ {package}: {version}")
        except ImportError:
            print(f"❌ {package}: 未安装")
            missing_packages.append(package)
    
    return len(missing_packages) == 0

def check_model_access():
    """检查模型访问权限"""
    print("\n🤗 检查模型访问...")
    try:
        from huggingface_hub import hf_hub_download
        # 尝试访问模型信息
        from diffusers import QwenImageTransformer2DModel
        config = QwenImageTransformer2DModel.load_config("Qwen/Qwen-Image", subfolder="transformer")
        print("✅ 可以访问Qwen/Qwen-Image模型")

        # 检查DFloat11模型
        from dfloat11 import DFloat11Model
        print("✅ DFloat11库可用")
        return True
    except Exception as e:
        print(f"❌ 模型访问失败: {e}")
        return False

def run_quick_test():
    """运行快速测试"""
    print("\n🧪 运行快速测试...")
    try:
        # 测试基本导入
        from diffusers import DiffusionPipeline, QwenImageTransformer2DModel
        from transformers.modeling_utils import no_init_weights
        from dfloat11 import DFloat11Model
        print("✅ 所有核心模块导入成功")
        
        # 测试CUDA内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            initial_memory = torch.cuda.memory_allocated()
            print(f"✅ 初始GPU内存使用: {initial_memory / 1024**2:.1f} MB")
        
        return True
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Qwen-Image DFloat11 部署测试")
    print("=" * 50)
    
    # 检查CUDA
    cuda_ok = check_cuda()
    
    # 检查包
    packages_ok = check_packages()
    
    # 检查模型访问
    model_ok = check_model_access()
    
    # 运行快速测试
    test_ok = run_quick_test()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"CUDA环境: {'✅' if cuda_ok else '❌'}")
    print(f"依赖包: {'✅' if packages_ok else '❌'}")
    print(f"模型访问: {'✅' if model_ok else '❌'}")
    print(f"快速测试: {'✅' if test_ok else '❌'}")
    
    if all([cuda_ok, packages_ok, model_ok, test_ok]):
        print("\n🎉 所有测试通过！环境配置正确。")
        print("\n💡 现在可以运行以下命令生成图像:")
        print("python qwen_image.py --cpu_offload --prompt '一只可爱的熊猫' --output test.png")
        return True
    else:
        print("\n⚠️  部分测试失败，请检查上述问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
