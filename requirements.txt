# PyTorch with CUDA 12.8 support
torch==2.8.0+cu128
torchvision==0.23.0+cu128
torchaudio==2.8.0+cu128

# DFloat11 compression library
dfloat11[cuda12]==0.5.0

# Diffusers and Transformers
git+https://github.com/huggingface/diffusers
transformers==4.56.1

# Core dependencies
accelerate==1.10.1
safetensors==0.6.2
huggingface-hub==0.34.4
tokenizers==0.22.0

# Image processing
Pillow==11.0.0

# Utilities
tqdm==4.67.1
pyyaml==6.0.2
requests==2.32.5
numpy==2.1.2
packaging==25.0

# CUDA support
cupy-cuda12x==13.6.0

# Additional dependencies
dahuffman==0.4.2
psutil==7.0.0
regex==2025.9.1
colorama==0.4.6
charset_normalizer==3.4.3
idna==3.10
urllib3==2.5.0
certifi==2025.8.3
fastrlock==0.8.3
importlib_metadata==8.7.0
zipp==3.23.0
