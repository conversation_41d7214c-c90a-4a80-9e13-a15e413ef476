from diffusers import Di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QwenImageTransformer2DModel
import torch
from transformers.modeling_utils import no_init_weights
from dfloat11 import DFloat11Model
import argparse
import os

def parse_args():
    parser = argparse.ArgumentParser(description='Generate images using local Qwen-Image DFloat11 model')
    parser.add_argument('--cpu_offload', action='store_true', default=True, help='Enable CPU offloading (default: True)')
    parser.add_argument('--cpu_offload_blocks', type=int, default=None, help='Number of transformer blocks to offload to CPU')
    parser.add_argument('--no_pin_memory', action='store_true', default=True, help='Disable memory pinning (default: True)')
    parser.add_argument('--prompt', type=str, default='一只可爱的熊猫在竹林中玩耍，阳光透过竹叶洒下斑驳的光影',
                        help='Text prompt for image generation')
    parser.add_argument('--negative_prompt', type=str, default=' ',
                        help='Negative prompt for image generation')
    parser.add_argument('--aspect_ratio', type=str, default='16:9', choices=['1:1', '16:9', '9:16', '4:3', '3:4'],
                        help='Aspect ratio of generated image')
    parser.add_argument('--num_inference_steps', type=int, default=30,
                        help='Number of denoising steps (default: 30 for faster generation)')
    parser.add_argument('--true_cfg_scale', type=float, default=4.0,
                        help='Classifier free guidance scale')
    parser.add_argument('--seed', type=int, default=42,
                        help='Random seed for generation')
    parser.add_argument('--output', type=str, default='generated_image.png',
                        help='Output image path')
    parser.add_argument('--language', type=str, default='zh', choices=['en', 'zh'],
                        help='Language for positive magic prompt')
    parser.add_argument('--local_model_path', type=str, default='./Qwen-Image-DF11',
                        help='Path to local DFloat11 model')
    return parser.parse_args()

def main():
    args = parse_args()

    # 检查本地模型路径
    if not os.path.exists(args.local_model_path):
        print(f"❌ 本地模型路径不存在: {args.local_model_path}")
        return

    print("🚀 使用本地Qwen-Image DFloat11模型生成图像")
    print("=" * 60)
    print(f"📁 本地模型路径: {args.local_model_path}")

    model_name = "Qwen/Qwen-Image"

    print("📥 加载Qwen-Image模型配置...")
    
    # Load transformer with no_init_weights to avoid loading weights twice
    with no_init_weights():
        transformer = QwenImageTransformer2DModel.from_config(
            QwenImageTransformer2DModel.load_config(
                model_name, subfolder="transformer",
            ),
        ).to(torch.bfloat16)

    # Load DFloat11 compressed model from local path
    print("📥 加载本地DFloat11压缩权重...")
    DFloat11Model.from_pretrained(
        args.local_model_path,  # 使用本地路径
        device="cpu",
        cpu_offload=args.cpu_offload,
        cpu_offload_blocks=args.cpu_offload_blocks,
        pin_memory=not args.no_pin_memory,
        bfloat16_model=transformer,
    )

    # Create diffusion pipeline
    print("📥 创建diffusion pipeline...")
    pipe = DiffusionPipeline.from_pretrained(
        model_name,
        transformer=transformer,
        torch_dtype=torch.bfloat16,
    )
    pipe.enable_model_cpu_offload()

    # Magic prompts for different languages
    positive_magic = {
        "en": "Ultra HD, 4K, cinematic composition.",  # for english prompt,
        "zh": "超清，4K，电影级构图"  # for chinese prompt,
    }

    # Generate with different aspect ratios
    aspect_ratios = {
        "1:1": (1328, 1328),
        "16:9": (1664, 928),
        "9:16": (928, 1664),
        "4:3": (1472, 1140),
        "3:4": (1140, 1472),
    }

    width, height = aspect_ratios[args.aspect_ratio]
    
    print(f"\n🎨 开始生成图像...")
    print(f"📝 提示词: {args.prompt}")
    print(f"📐 宽高比: {args.aspect_ratio} ({width}x{height})")
    print(f"🔢 推理步数: {args.num_inference_steps}")
    print(f"⚙️  CFG Scale: {args.true_cfg_scale}")
    print(f"🎲 随机种子: {args.seed}")
    
    if args.cpu_offload:
        print("💾 使用CPU offloading节省GPU内存")
    
    # Generate image
    start_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
    end_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
    
    if start_time:
        start_time.record()
    
    try:
        image = pipe(
            prompt=args.prompt + " " + positive_magic[args.language],
            negative_prompt=args.negative_prompt,
            width=width,
            height=height,
            num_inference_steps=args.num_inference_steps,
            true_cfg_scale=args.true_cfg_scale,
            generator=torch.Generator(device="cuda").manual_seed(args.seed)
        ).images[0]

        if end_time:
            end_time.record()
            torch.cuda.synchronize()
            generation_time = start_time.elapsed_time(end_time) / 1000.0  # Convert to seconds
        else:
            generation_time = 0

        # Save image
        image.save(args.output)
        print(f"\n✅ 图像生成完成！")
        print(f"💾 保存路径: {args.output}")
        
        if generation_time > 0:
            print(f"⏱️  生成时间: {generation_time:.1f} 秒")

        # Print memory usage
        if torch.cuda.is_available():
            max_memory = torch.cuda.max_memory_allocated()
            print(f"📊 最大GPU内存使用: {max_memory / (1000 ** 3):.2f} GB")
            
        print(f"\n🎉 成功！图像已保存到 {args.output}")
        
    except Exception as e:
        print(f"❌ 图像生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
