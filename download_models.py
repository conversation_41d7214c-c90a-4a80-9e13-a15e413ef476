#!/usr/bin/env python3
"""
预下载Qwen-Image DFloat11模型文件
"""

import os
import torch
from huggingface_hub import snapshot_download, hf_hub_download
from diffusers import DiffusionPipeline, QwenImageTransformer2DModel
from transformers.modeling_utils import no_init_weights
import time

# 延迟导入dfloat11，避免导入错误
try:
    from dfloat11 import DFloat11Model
    DFLOAT11_AVAILABLE = True
except ImportError:
    print("⚠️  dfloat11未正确安装，将跳过相关测试")
    DFLOAT11_AVAILABLE = False

def get_cache_dir():
    """获取HuggingFace缓存目录"""
    cache_dir = os.path.expanduser("~/.cache/huggingface/hub")
    print(f"HuggingFace缓存目录: {cache_dir}")
    return cache_dir

def download_base_model():
    """下载基础Qwen-Image模型"""
    print("📥 开始下载Qwen/Qwen-Image基础模型...")
    model_name = "Qwen/Qwen-Image"
    
    try:
        # 下载模型文件
        local_dir = snapshot_download(
            repo_id=model_name,
            cache_dir=get_cache_dir(),
            resume_download=True
        )
        print(f"✅ Qwen/Qwen-Image下载完成，位置: {local_dir}")
        return local_dir
    except Exception as e:
        print(f"❌ 下载Qwen/Qwen-Image失败: {e}")
        return None

def download_dfloat11_model():
    """下载DFloat11压缩模型"""
    print("📥 开始下载DFloat11/Qwen-Image-DF11压缩模型...")
    model_name = "DFloat11/Qwen-Image-DF11"
    
    try:
        # 下载压缩模型文件
        local_dir = snapshot_download(
            repo_id=model_name,
            cache_dir=get_cache_dir(),
            resume_download=True
        )
        print(f"✅ DFloat11/Qwen-Image-DF11下载完成，位置: {local_dir}")
        return local_dir
    except Exception as e:
        print(f"❌ 下载DFloat11/Qwen-Image-DF11失败: {e}")
        return None

def check_disk_space():
    """检查磁盘空间"""
    cache_dir = get_cache_dir()
    if os.path.exists(cache_dir):
        drive = os.path.splitdrive(cache_dir)[0]
    else:
        drive = os.path.splitdrive(os.path.expanduser("~"))[0]
    
    if os.name == 'nt':  # Windows
        import shutil
        _, _, free = shutil.disk_usage(drive + "\\")
        free_gb = free / (1024**3)
        print(f"💾 可用磁盘空间: {free_gb:.1f} GB")

        if free_gb < 60:
            print("⚠️  警告: 磁盘空间可能不足，建议至少有60GB可用空间")
            return False
        return True
    return True

def show_download_progress():
    """显示下载进度信息"""
    print("📊 模型下载信息:")
    print("- Qwen/Qwen-Image: ~41GB (基础模型)")
    print("- DFloat11/Qwen-Image-DF11: ~28GB (压缩模型)")
    print("- 总计需要: ~70GB 磁盘空间")
    print("- 下载时间: 取决于网络速度，可能需要30分钟到几小时")
    print()

def test_model_loading():
    """测试模型加载"""
    print("🧪 测试模型加载...")
    try:
        model_name = "Qwen/Qwen-Image"
        
        # 测试加载transformer配置
        with no_init_weights():
            transformer = QwenImageTransformer2DModel.from_config(
                QwenImageTransformer2DModel.load_config(
                    model_name, subfolder="transformer",
                ),
            ).to(torch.bfloat16)
        
        print("✅ 基础模型配置加载成功")
        
        # 测试DFloat11模型加载（不实际加载权重）
        print("✅ 模型文件可以正常访问")
        return True
        
    except Exception as e:
        print(f"❌ 模型加载测试失败: {e}")
        return False

def list_downloaded_files():
    """列出已下载的模型文件"""
    cache_dir = get_cache_dir()
    print(f"\n📁 检查已下载的模型文件 ({cache_dir}):")
    
    qwen_dir = os.path.join(cache_dir, "models--Qwen--Qwen-Image")
    dfloat_dir = os.path.join(cache_dir, "models--DFloat11--Qwen-Image-DF11")
    
    total_size = 0
    
    for model_dir, model_name in [(qwen_dir, "Qwen/Qwen-Image"), (dfloat_dir, "DFloat11/Qwen-Image-DF11")]:
        if os.path.exists(model_dir):
            size = get_dir_size(model_dir)
            total_size += size
            print(f"✅ {model_name}: {size/1024**3:.1f} GB")
        else:
            print(f"❌ {model_name}: 未下载")
    
    print(f"📊 总计已下载: {total_size/1024**3:.1f} GB")
    return total_size

def get_dir_size(path):
    """获取目录大小"""
    total = 0
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total += os.path.getsize(filepath)
    except:
        pass
    return total

def main():
    """主函数"""
    print("🚀 Qwen-Image DFloat11 模型下载器")
    print("=" * 60)
    
    # 检查磁盘空间
    if not check_disk_space():
        print("❌ 磁盘空间不足，请清理磁盘后重试")
        return
    
    # 显示下载信息
    show_download_progress()
    
    # 检查已下载的文件
    list_downloaded_files()
    
    # 询问是否继续下载
    response = input("\n是否开始下载模型？(y/n): ").lower().strip()
    if response != 'y':
        print("下载已取消")
        return
    
    start_time = time.time()
    
    # 下载基础模型
    base_model_path = download_base_model()
    if not base_model_path:
        print("❌ 基础模型下载失败")
        return
    
    # 下载DFloat11压缩模型
    dfloat_model_path = download_dfloat11_model()
    if not dfloat_model_path:
        print("❌ 压缩模型下载失败")
        return
    
    # 测试模型加载
    if test_model_loading():
        print("✅ 模型加载测试成功")
    
    # 显示最终结果
    end_time = time.time()
    download_time = end_time - start_time
    
    print("\n" + "=" * 60)
    print("🎉 模型下载完成！")
    print(f"⏱️  总下载时间: {download_time/60:.1f} 分钟")
    
    # 再次检查下载的文件
    list_downloaded_files()
    
    print("\n💡 现在可以运行图像生成:")
    print("python qwen_image.py --cpu_offload --prompt '一只可爱的熊猫' --output test.png")

if __name__ == "__main__":
    main()
