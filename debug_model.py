#!/usr/bin/env python3
"""
调试模型加载
"""

import torch
from diffusers import QwenImageTransformer2DModel
from transformers.modeling_utils import no_init_weights
import time

def debug_model_loading():
    print("🔍 调试模型加载过程")
    print("=" * 50)
    
    try:
        # 步骤1: 测试配置加载
        print("📥 步骤1: 加载配置...")
        model_name = "Qwen/Qwen-Image"
        config = QwenImageTransformer2DModel.load_config(model_name, subfolder="transformer")
        print("✅ 配置加载成功")
        print(f"配置: {config}")
        
        # 步骤2: 测试transformer创建（不加载权重）
        print("\n📥 步骤2: 创建transformer（无权重）...")
        with no_init_weights():
            transformer = QwenImageTransformer2DModel.from_config(config)
            print("✅ Transformer创建成功（无权重）")
            print(f"Transformer类型: {type(transformer)}")
        
        # 步骤3: 转换到bfloat16
        print("\n📥 步骤3: 转换到bfloat16...")
        transformer = transformer.to(torch.bfloat16)
        print("✅ 转换到bfloat16成功")
        
        # 步骤4: 移动到GPU（如果可用）
        if torch.cuda.is_available():
            print("\n📥 步骤4: 移动到GPU...")
            # 先不移动到GPU，保持在CPU
            print("✅ 保持在CPU以节省内存")
        
        # 检查内存使用
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.memory_allocated() / 1024**3
            print(f"🔧 当前GPU内存使用: {gpu_memory:.2f} GB")
        
        print("\n🎉 模型创建成功！")
        return transformer
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_dfloat11_loading(transformer):
    print("\n🔍 测试DFloat11加载")
    print("=" * 50)
    
    try:
        from dfloat11 import DFloat11Model
        
        local_model_path = "./Qwen-Image-DF11"
        print(f"📁 本地模型路径: {local_model_path}")
        
        print("📥 开始加载DFloat11权重...")
        start_time = time.time()
        
        DFloat11Model.from_pretrained(
            local_model_path,
            device="cpu",
            cpu_offload=True,
            pin_memory=False,
            bfloat16_model=transformer,
        )
        
        end_time = time.time()
        load_time = end_time - start_time
        
        print(f"✅ DFloat11权重加载成功！")
        print(f"⏱️  加载时间: {load_time:.1f} 秒")
        
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.memory_allocated() / 1024**3
            print(f"🔧 加载后GPU内存使用: {gpu_memory:.2f} GB")
        
        return True
        
    except Exception as e:
        print(f"❌ DFloat11加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 开始调试模型加载")
    
    # 清理GPU内存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print(f"🔧 初始GPU内存: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
    
    # 测试基础模型加载
    transformer = debug_model_loading()
    if transformer is None:
        print("❌ 基础模型加载失败，停止测试")
        return
    
    # 测试DFloat11加载
    if test_dfloat11_loading(transformer):
        print("\n🎉 所有测试通过！模型可以正常加载")
    else:
        print("\n❌ DFloat11加载失败")

if __name__ == "__main__":
    main()
