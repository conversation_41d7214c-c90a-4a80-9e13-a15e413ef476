#!/usr/bin/env python3
"""
测试本地Qwen-Image DFloat11模型
"""

import os
import torch
from diffusers import DiffusionPipeline, QwenImageTransformer2DModel
from transformers.modeling_utils import no_init_weights
from dfloat11 import DFloat11Model
import time

def test_local_model():
    """测试本地模型加载"""
    print("🧪 测试本地Qwen-Image DFloat11模型")
    print("=" * 50)
    
    # 检查本地模型文件
    local_model_path = "./Qwen-Image-DF11"
    if not os.path.exists(local_model_path):
        print("❌ 本地模型文件夹不存在")
        return False
    
    print(f"✅ 找到本地模型文件夹: {local_model_path}")
    
    # 检查必要文件
    required_files = ["config.json", "diffusion_pytorch_model.safetensors"]
    for file in required_files:
        file_path = os.path.join(local_model_path, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path) / (1024**3)
            print(f"✅ {file}: {size:.1f} GB")
        else:
            print(f"❌ 缺少文件: {file}")
            return False
    
    print("\n🔄 开始测试模型加载...")
    
    try:
        # 测试基础模型配置加载
        model_name = "Qwen/Qwen-Image"
        print("📥 加载基础模型配置...")
        
        with no_init_weights():
            transformer = QwenImageTransformer2DModel.from_config(
                QwenImageTransformer2DModel.load_config(
                    model_name, subfolder="transformer",
                ),
            ).to(torch.bfloat16)
        
        print("✅ 基础模型配置加载成功")
        
        # 测试DFloat11模型加载
        print("📥 加载DFloat11压缩权重...")
        DFloat11Model.from_pretrained(
            local_model_path,  # 使用本地路径
            device="cpu",
            cpu_offload=True,
            pin_memory=False,
            bfloat16_model=transformer,
        )
        
        print("✅ DFloat11压缩权重加载成功")
        
        # 测试pipeline创建
        print("📥 创建diffusion pipeline...")
        pipe = DiffusionPipeline.from_pretrained(
            model_name,
            transformer=transformer,
            torch_dtype=torch.bfloat16,
        )
        pipe.enable_model_cpu_offload()
        
        print("✅ Pipeline创建成功")
        
        # 检查GPU内存使用
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            memory_used = torch.cuda.memory_allocated() / (1024**3)
            print(f"📊 当前GPU内存使用: {memory_used:.2f} GB")
        
        print("\n🎉 模型加载测试完全成功！")
        print("💡 现在可以运行图像生成测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    if test_local_model():
        print("\n✅ 本地模型测试通过")
        print("🚀 可以运行以下命令生成图像:")
        print("python qwen_image_local.py --prompt '一只可爱的熊猫' --output test.png")
    else:
        print("\n❌ 本地模型测试失败")

if __name__ == "__main__":
    main()
