#!/usr/bin/env python3
"""
简单的图像生成测试
"""

import torch
from diffusers import DiffusionPipeline, QwenImageTransformer2DModel
from transformers.modeling_utils import no_init_weights
from dfloat11 import DFloat11Model
import time

def generate_image():
    print("🎨 开始图像生成测试")
    print("=" * 50)
    
    # 设置参数
    prompt = "A cute panda playing in bamboo forest"
    output_path = "test_output.png"
    local_model_path = "./Qwen-Image-DF11"
    
    print(f"📝 提示词: {prompt}")
    print(f"💾 输出路径: {output_path}")
    print(f"📁 本地模型: {local_model_path}")
    
    try:
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            print(f"🔧 初始GPU内存: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
        
        print("\n📥 步骤1: 加载基础模型配置...")
        model_name = "Qwen/Qwen-Image"
        
        with no_init_weights():
            transformer = QwenImageTransformer2DModel.from_config(
                QwenImageTransformer2DModel.load_config(
                    model_name, subfolder="transformer",
                ),
            ).to(torch.bfloat16)
        
        print("✅ 基础模型配置加载完成")
        
        print("\n📥 步骤2: 加载DFloat11压缩权重...")
        DFloat11Model.from_pretrained(
            local_model_path,
            device="cpu",
            cpu_offload=True,
            pin_memory=False,
            bfloat16_model=transformer,
        )
        
        print("✅ DFloat11权重加载完成")
        
        if torch.cuda.is_available():
            print(f"🔧 权重加载后GPU内存: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
        
        print("\n📥 步骤3: 创建Pipeline...")
        pipe = DiffusionPipeline.from_pretrained(
            model_name,
            transformer=transformer,
            torch_dtype=torch.bfloat16,
        )
        pipe.enable_model_cpu_offload()
        
        print("✅ Pipeline创建完成")
        
        if torch.cuda.is_available():
            print(f"🔧 Pipeline创建后GPU内存: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
        
        print("\n🎨 步骤4: 开始生成图像...")
        start_time = time.time()
        
        # 生成参数
        width, height = 1664, 928  # 16:9
        num_steps = 20  # 较少步数用于测试
        
        print(f"📐 图像尺寸: {width}x{height}")
        print(f"🔢 推理步数: {num_steps}")
        
        image = pipe(
            prompt=prompt + " Ultra HD, 4K, cinematic composition.",
            negative_prompt=" ",
            width=width,
            height=height,
            num_inference_steps=num_steps,
            true_cfg_scale=4.0,
            generator=torch.Generator(device="cuda").manual_seed(42)
        ).images[0]
        
        end_time = time.time()
        generation_time = end_time - start_time
        
        # 保存图像
        image.save(output_path)
        
        print(f"\n🎉 图像生成成功！")
        print(f"💾 保存到: {output_path}")
        print(f"⏱️  生成时间: {generation_time:.1f} 秒")
        
        if torch.cuda.is_available():
            max_memory = torch.cuda.max_memory_allocated() / 1024**3
            print(f"📊 最大GPU内存使用: {max_memory:.2f} GB")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if generate_image():
        print("\n✅ 图像生成测试成功！")
    else:
        print("\n❌ 图像生成测试失败")
