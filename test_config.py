#!/usr/bin/env python3
"""
测试配置加载
"""

from diffusers import QwenImageTransformer2DModel

def test_config():
    print("测试配置加载...")
    try:
        config = QwenImageTransformer2DModel.load_config("Qwen/Qwen-Image", subfolder="transformer")
        print("✅ 配置加载成功")
        print(f"配置内容: {config}")
        return True
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

if __name__ == "__main__":
    test_config()
